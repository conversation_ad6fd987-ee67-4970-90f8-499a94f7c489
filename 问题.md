# Chat Messages API 接口文档

## 接口概述

发送聊天消息的 API 接口，支持流式响应和文件上传功能。

## 接口详情

### 请求信息

- **请求方法**: `POST`
- **请求 URL**: `http://*************/v1/chat-messages`
- **Content-Type**: `application/json`

### 请求头 (Headers)

| 参数名        | 类型   | 必填 | 说明                                        |
| ------------- | ------ | ---- | ------------------------------------------- |
| Authorization | string | 是   | Bearer token 认证，格式：`Bearer {api_key}` |
| Content-Type  | string | 是   | 固定值：`application/json`                  |

### 请求参数 (Request Body)

| 参数名          | 类型   | 必填 | 说明                                                        |
| --------------- | ------ | ---- | ----------------------------------------------------------- |
| inputs          | object | 否   | 输入参数对象                                                |
| query           | string | 是   | 用户查询内容                                                |
| response_mode   | string | 是   | 响应模式，可选值：`streaming`（流式）、`blocking`（阻塞式） |
| conversation_id | string | 否   | 会话 ID，用于继续之前的对话                                 |
| user            | string | 是   | 用户标识符                                                  |
| files           | array  | 否   | 文件列表                                                    |

#### files 数组元素结构

| 参数名          | 类型   | 必填 | 说明                                                |
| --------------- | ------ | ---- | --------------------------------------------------- |
| type            | string | 是   | 文件类型，如：`image`                               |
| transfer_method | string | 是   | 传输方式，可选值：`remote_url`、`local_file`        |
| url             | string | 否   | 文件 URL（当 transfer_method 为 remote_url 时必填） |

### 请求示例

#### cURL 示例

```bash
curl -X POST 'http://*************/v1/chat-messages' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "inputs": {},
    "query": "What are the specs of the iPhone 13 Pro Max?",
    "response_mode": "streaming",
    "conversation_id": "",
    "user": "abc-123",
    "files": [
      {
        "type": "image",
        "transfer_method": "remote_url",
        "url": "https://cloud.dify.ai/logo/logo-site.png"
      }
    ]
}'
```

#### JavaScript 示例

```javascript
const response = await fetch('http://*************/v1/chat-messages', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer {api_key}',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    inputs: {},
    query: 'What are the specs of the iPhone 13 Pro Max?',
    response_mode: 'streaming',
    conversation_id: '',
    user: 'abc-123',
    files: [
      {
        type: 'image',
        transfer_method: 'remote_url',
        url: 'https://cloud.dify.ai/logo/logo-site.png',
      },
    ],
  }),
})
```

### 响应格式

#### 阻塞式响应 (response_mode: "blocking")

返回完整的 JSON 响应对象。

**响应示例：**

```json
{
  "event": "message",
  "task_id": "c3800678-a077-43df-a102-53f23ed20b88",
  "id": "9da23599-e713-473b-982c-4328d4f5c78a",
  "message_id": "9da23599-e713-473b-982c-4328d4f5c78a",
  "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2",
  "mode": "chat",
  "answer": "iPhone 13 Pro Max specs are listed here:...",
  "metadata": {
    "usage": {
      "prompt_tokens": 1033,
      "prompt_unit_price": "0.001",
      "prompt_price_unit": "0.001",
      "prompt_price": "0.0010330",
      "completion_tokens": 128,
      "completion_unit_price": "0.002",
      "completion_price_unit": "0.001",
      "completion_price": "0.0002560",
      "total_tokens": 1161,
      "total_price": "0.0012890",
      "currency": "USD",
      "latency": 0.7682376249867957
    },
    "retriever_resources": [
      {
        "position": 1,
        "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
        "dataset_name": "iPhone",
        "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
        "document_name": "iPhone List",
        "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
        "score": 0.98457545,
        "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
      }
    ]
  },
  "created_at": 1705407629
}
```

#### 流式响应 (response_mode: "streaming")

当使用流式响应时，服务器会返回 Server-Sent Events (SSE) 格式的数据流。每个事件以 `data:` 开头，包含不同类型的事件。

**响应示例：**

```
data: {"event": "workflow_started", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "workflow_id": "dfjasklfjdslag", "created_at": 1679586595}}

data: {"event": "node_started", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "node_id": "dfjasklfjdslag", "node_type": "start", "title": "Start", "index": 0, "predecessor_node_id": "fdljewklfklgejlglsd", "inputs": {}, "created_at": 1679586595}}

data: {"event": "node_finished", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "node_id": "dfjasklfjdslag", "node_type": "start", "title": "Start", "index": 0, "predecessor_node_id": "fdljewklfklgejlglsd", "inputs": {}, "outputs": {}, "status": "succeeded", "elapsed_time": 0.324, "execution_metadata": {"total_tokens": 63127864, "total_price": 2.378, "currency": "USD"},  "created_at": 1679586595}}

data: {"event": "workflow_finished", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "workflow_id": "dfjasklfjdslag", "outputs": {}, "status": "succeeded", "elapsed_time": 0.324, "total_tokens": 63127864, "total_steps": "1", "created_at": 1679586595, "finished_at": 1679976595}}

data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " I", "created_at": 1679586595}

data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": "'m", "created_at": 1679586595}

data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " glad", "created_at": 1679586595}

data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " to", "created_at": 1679586595}

data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " meet", "created_at": 1679586595}

data: {"event": "message", "message_id" : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " you", "created_at": 1679586595}

data: {"event": "message_end", "id": "5e52ce04-874b-4d27-9045-b3bc80def685", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "metadata": {"usage": {"prompt_tokens": 1033, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0010330", "completion_tokens": 135, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0002700", "total_tokens": 1168, "total_price": "0.0013030", "currency": "USD", "latency": 1.381760165997548}, "retriever_resources": [{"position": 1, "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb", "dataset_name": "iPhone", "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00", "document_name": "iPhone List", "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a", "score": 0.98457545, "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""}]}}

data: {"event": "tts_message", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": "qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"}

data: {"event": "tts_message_end", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": ""}
```

**流式响应事件类型说明：**

| 事件类型          | 说明                       |
| ----------------- | -------------------------- |
| workflow_started  | 工作流开始                 |
| node_started      | 节点开始执行               |
| node_finished     | 节点执行完成               |
| workflow_finished | 工作流完成                 |
| message           | 消息内容（逐字返回）       |
| message_end       | 消息结束，包含完整的元数据 |
| tts_message       | TTS 音频消息               |
| tts_message_end   | TTS 音频消息结束           |

### 注意事项

1. 请确保 `api_key` 有效且具有相应权限
2. 当使用图片等文件时，确保文件 URL 可访问
3. `user` 参数用于用户会话管理和统计
4. `conversation_id` 为空时会创建新会话，非空时会继续指定会话

---

## 获取会话历史消息接口

### 接口概述

获取指定用户和会话的历史消息记录。

### 请求信息

- **请求方法**: `GET`
- **请求 URL**: `http://*************/v1/messages`
- **Content-Type**: `application/json`

### 请求头 (Headers)

| 参数名        | 类型   | 必填 | 说明                                        |
| ------------- | ------ | ---- | ------------------------------------------- |
| Authorization | string | 是   | Bearer token 认证，格式：`Bearer {api_key}` |

### 查询参数 (Query Parameters)

| 参数名          | 类型   | 必填 | 说明                                  |
| --------------- | ------ | ---- | ------------------------------------- |
| user            | string | 是   | 用户标识符                            |
| conversation_id | string | 否   | 会话 ID，为空时获取用户的所有会话消息 |
| limit           | number | 否   | 返回消息数量限制，默认为 20           |

### 请求示例

#### cURL 示例

```bash
curl -X GET 'http://*************/v1/messages?user=abc-123&conversation_id=' \
--header 'Authorization: Bearer {api_key}'
```

#### JavaScript 示例

```javascript
const response = await fetch(
  'http://*************/v1/messages?user=abc-123&conversation_id=',
  {
    method: 'GET',
    headers: {
      Authorization: 'Bearer {api_key}',
    },
  }
)

const data = await response.json()
```

### 响应格式

**响应示例：**

```json
{
  "limit": 20,
  "has_more": false,
  "data": [
    {
      "id": "a076a87f-31e5-48dc-b452-0061adbbc922",
      "conversation_id": "cd78daf6-f9e4-4463-9ff2-54257230a0ce",
      "inputs": {
        "name": "dify"
      },
      "query": "iphone 13 pro",
      "answer": "The iPhone 13 Pro, released on September 24, 2021, features a 6.1-inch display with a resolution of 1170 x 2532. It is equipped with a Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard) processor, 6 GB of RAM, and offers storage options of 128 GB, 256 GB, 512 GB, and 1 TB. The camera is 12 MP, the battery capacity is 3095 mAh, and it runs on iOS 15.",
      "message_files": [],
      "feedback": null,
      "retriever_resources": [
        {
          "position": 1,
          "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
          "dataset_name": "iPhone",
          "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
          "document_name": "iPhone List",
          "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
          "score": 0.98457545,
          "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
        }
      ],
      "created_at": 1705569239
    }
  ]
}
```

### 响应字段说明

#### 根级别字段

| 字段名   | 类型    | 说明                   |
| -------- | ------- | ---------------------- |
| limit    | number  | 本次请求的消息数量限制 |
| has_more | boolean | 是否还有更多消息       |
| data     | array   | 消息列表               |

#### data 数组元素字段

| 字段名              | 类型        | 说明             |
| ------------------- | ----------- | ---------------- |
| id                  | string      | 消息唯一标识符   |
| conversation_id     | string      | 会话 ID          |
| inputs              | object      | 输入参数         |
| query               | string      | 用户查询内容     |
| answer              | string      | AI 回答内容      |
| message_files       | array       | 消息附件文件列表 |
| feedback            | object/null | 用户反馈信息     |
| retriever_resources | array       | 检索到的资源信息 |
| created_at          | number      | 消息创建时间戳   |

---

## 获取会话列表接口

### 接口概述

获取指定用户的会话列表，支持分页查询。

### 请求信息

- **请求方法**: `GET`
- **请求 URL**: `http://*************/v1/conversations`
- **Content-Type**: `application/json`

### 请求头 (Headers)

| 参数名        | 类型   | 必填 | 说明                                        |
| ------------- | ------ | ---- | ------------------------------------------- |
| Authorization | string | 是   | Bearer token 认证，格式：`Bearer {api_key}` |

### 查询参数 (Query Parameters)

| 参数名  | 类型   | 必填 | 说明                                |
| ------- | ------ | ---- | ----------------------------------- |
| user    | string | 是   | 用户标识符                          |
| last_id | string | 否   | 上次查询的最后一个会话 ID，用于分页 |
| limit   | number | 否   | 返回会话数量限制，默认为 20         |

### 请求示例

#### cURL 示例

```bash
curl -X GET 'http://*************/v1/conversations?user=abc-123&last_id=&limit=20' \
--header 'Authorization: Bearer {api_key}'
```

#### JavaScript 示例

```javascript
const response = await fetch(
  'http://*************/v1/conversations?user=abc-123&last_id=&limit=20',
  {
    method: 'GET',
    headers: {
      Authorization: 'Bearer {api_key}',
    },
  }
)

const data = await response.json()
```

### 响应格式

**响应示例：**

```json
{
  "limit": 20,
  "has_more": false,
  "data": [
    {
      "id": "10799fb8-64f7-4296-bbf7-b42bfbe0ae54",
      "name": "New chat",
      "inputs": {
        "book": "book",
        "myName": "Lucy"
      },
      "status": "normal",
      "created_at": 1679667915,
      "updated_at": 1679667915
    },
    {
      "id": "hSIhXBhNe8X1d8Et"
      // ...
    }
  ]
}
```

### 响应字段说明

#### 根级别字段

| 字段名   | 类型    | 说明                   |
| -------- | ------- | ---------------------- |
| limit    | number  | 本次请求的会话数量限制 |
| has_more | boolean | 是否还有更多会话       |
| data     | array   | 会话列表               |

#### data 数组元素字段

| 字段名     | 类型   | 说明                         |
| ---------- | ------ | ---------------------------- |
| id         | string | 会话唯一标识符               |
| name       | string | 会话名称                     |
| inputs     | object | 会话输入参数                 |
| status     | string | 会话状态，如：normal（正常） |
| created_at | number | 会话创建时间戳               |
| updated_at | number | 会话更新时间戳               |

### 分页说明

- 首次请求时，`last_id` 参数可以为空
- 如果 `has_more` 为 `true`，表示还有更多数据
- 下次请求时，将上次响应中最后一个会话的 `id` 作为 `last_id` 参数传入

---

## 语音转文字接口

### 接口概述

将音频文件转换为文字的接口，支持多种音频格式。

### 请求信息

- **请求方法**: `POST`
- **请求 URL**: `http://*************/v1/audio-to-text`
- **Content-Type**: `multipart/form-data`

### 请求头 (Headers)

| 参数名        | 类型   | 必填 | 说明                                        |
| ------------- | ------ | ---- | ------------------------------------------- |
| Authorization | string | 是   | Bearer token 认证，格式：`Bearer {api_key}` |

### 请求参数 (Form Data)

| 参数名 | 类型 | 必填 | 说明                                                     |
| ------ | ---- | ---- | -------------------------------------------------------- |
| file   | file | 是   | 音频文件，支持格式：mp3, mp4, mpeg, mpga, m4a, wav, webm |

### 请求示例

#### cURL 示例

```bash
curl -X POST 'http://*************/v1/audio-to-text' \
--header 'Authorization: Bearer {api_key}' \
--form 'file=@localfile;type=audio/mp3'
```

#### JavaScript 示例

```javascript
const formData = new FormData()
formData.append('file', audioFile) // audioFile 是一个 File 对象

const response = await fetch('http://*************/v1/audio-to-text', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer {api_key}',
  },
  body: formData,
})

const data = await response.json()
```

#### Python 示例

```python
import requests

url = "http://*************/v1/audio-to-text"
headers = {
    "Authorization": "Bearer {api_key}"
}

with open("audio_file.mp3", "rb") as f:
    files = {"file": ("audio_file.mp3", f, "audio/mp3")}
    response = requests.post(url, headers=headers, files=files)

result = response.json()
```

### 响应格式

**响应示例：**

```json
{
  "text": "hello"
}
```

### 响应字段说明

| 字段名 | 类型   | 说明             |
| ------ | ------ | ---------------- |
| text   | string | 转换后的文字内容 |

### 支持的音频格式

- **mp3** - MPEG Audio Layer III
- **mp4** - MPEG-4 音频
- **mpeg** - MPEG 音频
- **mpga** - MPEG Audio
- **m4a** - MPEG-4 Audio
- **wav** - Waveform Audio File Format
- **webm** - WebM 音频

### 注意事项

1. 音频文件大小建议不超过 25MB
2. 音频时长建议不超过 30 分钟
3. 支持的采样率：8kHz - 48kHz
4. 确保音频文件格式正确，避免转换失败

---

## 错误码

| 状态码 | 说明             |
| ------ | ---------------- |
| 200    | 请求成功         |
| 400    | 请求参数错误     |
| 401    | 认证失败         |
| 403    | 权限不足         |
| 413    | 文件过大         |
| 415    | 不支持的媒体类型 |
| 500    | 服务器内部错误   |
