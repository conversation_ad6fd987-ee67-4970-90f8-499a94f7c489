# Chat Messages API 接口文档

## 接口概述

发送聊天消息的 API 接口，支持流式响应和文件上传功能。

## 接口详情

### 请求信息

- **请求方法**: `POST`
- **请求 URL**: `http://*************/v1/chat-messages`
- **Content-Type**: `application/json`

### 请求头 (Headers)

| 参数名        | 类型   | 必填 | 说明                                        |
| ------------- | ------ | ---- | ------------------------------------------- |
| Authorization | string | 是   | Bearer token 认证，格式：`Bearer {api_key}` |
| Content-Type  | string | 是   | 固定值：`application/json`                  |

### 请求参数 (Request Body)

| 参数名          | 类型   | 必填 | 说明                                                        |
| --------------- | ------ | ---- | ----------------------------------------------------------- |
| inputs          | object | 否   | 输入参数对象                                                |
| query           | string | 是   | 用户查询内容                                                |
| response_mode   | string | 是   | 响应模式，可选值：`streaming`（流式）、`blocking`（阻塞式） |
| conversation_id | string | 否   | 会话 ID，用于继续之前的对话                                 |
| user            | string | 是   | 用户标识符                                                  |
| files           | array  | 否   | 文件列表                                                    |

#### files 数组元素结构

| 参数名          | 类型   | 必填 | 说明                                                |
| --------------- | ------ | ---- | --------------------------------------------------- |
| type            | string | 是   | 文件类型，如：`image`                               |
| transfer_method | string | 是   | 传输方式，可选值：`remote_url`、`local_file`        |
| url             | string | 否   | 文件 URL（当 transfer_method 为 remote_url 时必填） |

### 请求示例

#### cURL 示例

```bash
curl -X POST 'http://*************/v1/chat-messages' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "inputs": {},
    "query": "What are the specs of the iPhone 13 Pro Max?",
    "response_mode": "streaming",
    "conversation_id": "",
    "user": "abc-123",
    "files": [
      {
        "type": "image",
        "transfer_method": "remote_url",
        "url": "https://cloud.dify.ai/logo/logo-site.png"
      }
    ]
}'
```

#### JavaScript 示例

```javascript
const response = await fetch('http://*************/v1/chat-messages', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer {api_key}',
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    inputs: {},
    query: 'What are the specs of the iPhone 13 Pro Max?',
    response_mode: 'streaming',
    conversation_id: '',
    user: 'abc-123',
    files: [
      {
        type: 'image',
        transfer_method: 'remote_url',
        url: 'https://cloud.dify.ai/logo/logo-site.png',
      },
    ],
  }),
})
```

### 响应格式

#### 流式响应 (response_mode: "streaming")

当使用流式响应时，服务器会返回 Server-Sent Events (SSE) 格式的数据流。

#### 阻塞式响应 (response_mode: "blocking")

返回完整的 JSON 响应对象。

### 注意事项

1. 请确保 `api_key` 有效且具有相应权限
2. 当使用图片等文件时，确保文件 URL 可访问
3. `user` 参数用于用户会话管理和统计
4. `conversation_id` 为空时会创建新会话，非空时会继续指定会话

### 错误码

| 状态码 | 说明           |
| ------ | -------------- |
| 200    | 请求成功       |
| 400    | 请求参数错误   |
| 401    | 认证失败       |
| 403    | 权限不足       |
| 500    | 服务器内部错误 |
